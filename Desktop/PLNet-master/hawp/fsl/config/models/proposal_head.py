from yacs.config import CfgNode as CN

PROPOSAL_HEAD = CN()

PROPOSAL_HEAD.NUM_PROPOSALS = 512
PROPOSAL_HEAD.ANGULAR_THRESHOLD = 0.01 #TODO: REMOVE
PROPOSAL_HEAD.NUM_SAMPLE_POINTS = 32
PROPOSAL_HEAD.POSITIVE_DIS_TH = 10.0
PROPOSAL_HEAD.NEGATIVE_DIS_TH = 15.0
PROPOSAL_HEAD.LOWEST_SCORE_TH = 0.05
PROPOSAL_HEAD.POST_NMS_TH    = 10.0
PROPOSAL_HEAD.USE_1DPOOLING  = False
PROPOSAL_HEAD.MIN_DISTANCE   = 0.0
PROPOSAL_HEAD.MAX_DISTANCE   = 1.5
PROPOSAL_HEAD.NUM_DIS_PROPOSAL = 9
PROPOSAL_HEAD.USE_EDGE       = False
PROPOSAL_HEAD.SHARE_WITH_JUNCTION_FEATURE = False


PROPOSAL_HEAD.NUM_DYNAMIC_JUNCTIONS = 300
PROPOSAL_HEAD.NUM_DYNAMIC_POSITIVE_LINES = 300
PROPOSAL_HEAD.NUM_DYNAMIC_NEGATIVE_LINES = 80
PROPOSAL_HEAD.NUM_DYNAMIC_OTHER_LINES = 600

# PROPOSAL_HEAD.LOSS_WEIGHTS = CN()
# PROPOSAL_HEAD.LOSS_WEIGHTS.AFM = 1.0
# PROPOSAL_HEAD.LOSS_WEIGHTS.PROPOSAL = 1.0
