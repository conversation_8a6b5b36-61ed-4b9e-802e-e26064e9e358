# Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved.
import torch

import importlib
import importlib.util
import sys

def import_file(module_name, file_path, make_importable=False):
    spec = importlib.util.spec_from_file_location(module_name, file_path)
    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    if make_importable:
        sys.modules[module_name] = module
    return module
